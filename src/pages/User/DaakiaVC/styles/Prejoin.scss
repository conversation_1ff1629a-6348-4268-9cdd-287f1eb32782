@import "./mixins";
@import "./variables";

.prejoin-parent-container {
  @include pre-join-inner-container;
  height: 100svh;
  font-size: 16px;

  @media screen and (max-width: 480px) {
    padding-top: 0;
  }

  .prejoin-logo {
    display: flex;
    justify-content: center;
    margin-bottom: 0.75rem;

    svg {
      width: 24px !important;
      height: 30px !important;

      @media screen and (max-width: 768px) {
        width: 20px !important;
        height: 25px !important;
      }

      @media screen and (max-width: 480px) {
        width: 20px !important;
        height: 25px !important;
      }
    }

    &-electron {
      margin-top: 0.75rem;
    }
  }

  .prejoin-left-inner-container {
    @include pre-join-inner-container;
    width: 100%;
    height: 100%;

    // .prejoin-mobile-view-heading {
    //   display: none;
    //   @media screen and (max-width: 480px) {
    //     margin-top: 2rem;
    //   }
    // }

    // .prejoin-left-inner-description {
    //   color: $prejoin-left-inner-description-color;
    //   font-size: 1em;
    //   font-weight: 400;
    //   margin-top: 1rem;

    //   @media (max-width: 1200px) {
    //     margin-top: 0.8rem;
    //   }

    //   @media (max-width: 768px) {
    //     margin-top: 0.5rem;
    //   }

    //   @media (max-width: 480px) {
    //     margin-top: 0.3rem;
    //   }
    // }

    // .lk-prejoin {
    //   box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2),
    //     0 6px 20px 0 rgba(0, 0, 0, 0.19);
    //   border-radius: 10px;
    //   padding: 0 !important;
    //   margin: 0 !important;
    // }
  }

  .prejoin-right-inner-container {
    @include pre-join-inner-container;
    width: 100%;
    height: 100%;

    .container-parent-form {
      width: 100%;
      height: 100%;

      // .join-meeting-title {
      //   font-size: 2.3rem;
      //   font-weight: 400;
      //   color: $join-meeting-title-color;
      //   margin-bottom: 20px;
      //   font-family: $font;

      //   @media (max-width: 1200px) {
      //     font-size: 1.8rem !important;
      //   }

      //   @media (max-width: 940px) {
      //     font-size: 1.5rem !important;
      //   }

      //   @media (max-width: 1040px) {
      //     font-size: 1.7rem;
      //   }

      //   @media (max-width: 890px) {
      //     font-size: 1.5rem;
      //   }

      //   @media (max-width: 785px) {
      //     font-size: 1.3rem;
      //   }
      // }

      .form-input-container {
        width: 100%;
        margin-bottom: 0.75rem;

        label {
          display: block;
          font-size: 1rem;
          font-weight: 500;
          color: #4A4A4A;
          margin-bottom: 0.5rem;
          font-family: 'Inter', sans-serif;
        }

        // Add styles for required field indicator
        .text-danger {
          color: #ff4d4f;
          margin-left: 4px;
        }

        // Remove additional spacing between input fields
        & + .form-input-container {
          margin-top: 0;
        }
      }
    }
  }
}

.container-parent-form {
  transition: all 0.3s ease;
  transform-origin: center center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .button-container {
    display: flex;
    justify-content: flex-end;
    width: 75%;
    margin-top: 40px;
    gap: 1rem;

    .go-back-link{
      display: flex;
      align-items: center;
      text-decoration: underline;
      font-size: 18px;
    }

    // .join-button-disabled {
    //   @include pre-join-button;
    //   background-color: $join-button-disabled;
    //   pointer-events: none; /* Make the button not clickable */
    //   cursor: not-allowed; /* Show a "not-allowed" cursor */

    //   span {
    //     font-family: $font;
    //   }
    // }

  //   .join-button-enabled {
  //     @include pre-join-button;
  //     background-color: $join-button-enabled;
  //     box-shadow: 0 4px 8px 0 #b9dcff, 0 1px 2px 0 #c8ccd2;

  //     span {
  //       font-family: $font;
  //     }
  //   }
  }
}

@media (max-width: 480px) {
  .prejoin-parent-container {
    .ant-row {
      flex-direction: column;
      flex-wrap: nowrap;
      align-items: center;
      position: relative;
      .prejoin-left-inner-container {
        max-width: 90%;
        .prejoin-mobile-view-heading {
          display: block;
          font-size: clamp(16px, 5vw, 20px);
          font-weight: 600;
          color: #000000;
          margin-bottom: 20px;
        }
        .button-icon {
          svg {
            width: 18px;
            height: 20px;
          }
        }
      }
      .prejoin-right-inner-container {
        max-width: 90%;
        .container-parent-form {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transform-origin: center center;
          .form-input-container {
            p {
              font-size: clamp(14px, 3.5vw, 16px);
            }
          }
          .join-meeting-title {
            display: none;
          }
          .button-container {
            width: auto;
            margin-top: 1rem;
          }
        }
      }
    }
  }
}

@media (max-width: 1040px) {
  .form-input-container p {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
  }
}

@media (max-width: 890px) {
  .form-input-container p {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
  }
}

@media (max-width: 785px) {
  .form-input-container p {
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
  }
}

// PrejoinAudioVideo.js
.button-icon {
  padding: 8px 10px;
  svg {
    width: 31px;
    height: 30px;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.not-allowed {
  .loading-title {
    @media screen and (max-width: 1200px) {
      font-size: 1.6rem;
    }
    @media screen and (max-width: 960px) {
      font-size: 1.2rem;
    }
  }
}

@media screen and (max-width: 450px) {
  .loading-title {
    text-align: center !important;
    font-size: 1.2rem !important;
  }
  .loading-description {
    text-align: center !important;
  }
}

// Loader.js
.loading-parent-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .loading-title {
    font-size: 2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 20px;
    font-family: $font;
  }
  .loading-description {
    color: #000000;
    font-size: 1em;
    font-weight: 400;
    margin-bottom: 20px;
    font-family: $font;
  }
  .rotate {
    animation: rotate 1s linear infinite;
    width: 31px;
    height: 30px;
  }
}
.prejoin-toast {
  .toast{
    width: auto;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #2b2b2b;
    padding: 5px 10px;
    color: white;
    font-size: 12px;
    border-radius: 3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    max-width: 200px;
    text-align: center;

  }
}


// PrejoinAudioVideo.js

// .lk-prejoin{
//   // position: relative;
//   // padding: 0 !important;
//   // margin: 0 !important;
//   .lk-button-group-container {
//     .lk-button-group {
//       width: auto !important;
//       display: flex;
//       align-items: center;
//       gap: 0.65rem;
//     }
//   }
// }
.mirrored-video {
  transform: scaleX(-1) !important;
  -webkit-transform: scaleX(-1) !important; /* For older browsers */
}
.not-mirrored-video{
  transform: scaleX(1) !important;
  -webkit-transform: scaleX(1) !important; /* For older browsers */
}

// Prejoin page specific styles
.prejoin-page-heading {
  font-family: 'Inter';
  font-weight: 600;
  font-size: clamp(20px, 4vw, 38.24px);
  line-height: 1.2;
  letter-spacing: 0.28px;
  color: #4A4A4A;
  margin-bottom: 1rem;

  @media screen and (max-width: 480px) {
    font-size: clamp(18px, 5vw, 24px);
  }

  @media screen and (max-width: 360px) {
    font-size: clamp(16px, 4.5vw, 20px);
  }
}

.prejoin-page-description {
  font-family: 'Inter';
  font-weight: 400;
  font-size: clamp(14px, 3vw, 16px);
  line-height: 1.6;
  letter-spacing: 0.56px;
  color: #4A4A4A;
  max-width: 90%;
  margin-bottom: 3rem;

  @media screen and (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
}

.prejoin-page-input {
  width: 100%;
  height: clamp(40px, 4vw, 53.98px);
  border-radius: 9px;
  border: 1.12px solid #C5C5C5;
  padding: 0 clamp(12px, 2vw, 17.99px);
  gap: clamp(12px, 2vw, 17.99px);
  font-size: clamp(14px, 1.5vw, 16px);
  display: flex;
  align-items: center;

  &::placeholder {
    vertical-align: middle;
  }

  &.error {
    border-color: #ff4d4f;

    &:hover, &:focus {
      border-color: #ff7875;
    }
  }
}

// Add styles for password fields
.ant-input-password {
  width: 100%;
  height: clamp(40px, 4vw, 53.98px);
  border-radius: 9px;
  border: 1.12px solid #C5C5C5;
  padding: 0 clamp(12px, 2vw, 17.99px);
  gap: clamp(12px, 2vw, 17.99px);
  display: flex;
  align-items: center;

  .ant-input {
    height: 100%;
    font-size: clamp(14px, 1.5vw, 16px);
    font-family: 'Inter', sans-serif;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .anticon {
    color: #4A4A4A;
  }
}

// Make password placeholder align with normal input
.ant-input-password .ant-input {
  &::placeholder {
    padding-left: 0 !important;
    color: #bfbfbf;
    opacity: 1;
  }
}

.prejoin-page-join-button {
  width: 100%;
  height: clamp(40px, 4vw, 53.98px);
  border-radius: 9px;
  margin-top: 0.5rem;
  background-color: #2572ED;
  border-color: #2572ED;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: clamp(14px, 1.5vw, 16px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px !important;

  &:hover:not(:disabled) {
    transform: scale(1.02);
    background-color: #1a5fd9;
    border-color: #1a5fd9;
    box-shadow: 0 4px 12px rgba(37, 114, 237, 0.2);
  }

  &:disabled {
    background-color: rgba(37, 114, 237, 0.5) !important;
    border-color: rgba(37, 114, 237, 0.5) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.8;
  }
}

// Improve mobile responsiveness
@media (max-width: 768px) {
  .form-input-container {
    width: 90%;

    p {
      font-size: 0.9rem;
    }
  }

  // Adjust side-by-side layout for smaller screens
  .prejoin-form.name-only {
    .form-input-container {
      gap: 0.5rem;

      .ant-input, .ant-input-affix-wrapper {
        height: 38px !important;
      }

      .prejoin-page-join-button {
        min-width: 80px;
        padding: 0 10px !important;
        height: 38px !important;
      }
    }
  }
}

.prejoin-form {
  width: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  margin: 0 auto;
  transform-origin: center center;

  // Ensure form grows from center when fields are added
  &:not(.name-only) {
    .form-input-container {
      margin-bottom: 0.75rem;
      transition: all 0.3s ease;
    }
  }

  // Add styles for name-only case (side-by-side layout)
  &.name-only {
    .form-input-container {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 0;

      .ant-input, .ant-input-affix-wrapper {
        flex: 1;
        height: 40px !important;
        display: flex;
        align-items: center;
      }

      .ant-input {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }

      .prejoin-page-join-button {
        width: auto;
        min-width: 120px;
        margin-top: 0;
        height: 40px !important; // Match input height
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px !important;
      }
    }
  }
}

// Override Ant Design's input affix wrapper style
.ant-input-affix-wrapper:before {
  display: none !important;
}

@media (max-width: 700px) {
  .responsive-logo {
    transform: scale(0.9);
    transform-origin: center;
  }

  .responsive-heading {
    font-size: clamp(18px, 4vw, 22px);
  }

  .responsive-description {
    font-size: 0.85rem;
  }

  .prejoin-page-input,
  .ant-input-password,
  .prejoin-page-join-button {
    height: 38px !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
  }
}

@media (max-width: 480px) {
  .responsive-heading {
    font-size: clamp(16px, 4.5vw, 20px);
  }

  .responsive-description {
    font-size: 0.8rem;
  }

  // Further adjust side-by-side layout for very small screens
  .prejoin-form.name-only {
    .form-input-container {
      flex-direction: column;
      gap: 0.5rem;

      .ant-input {
        width: 100%;
      }

      .prejoin-page-join-button {
        width: 100% !important;
        min-width: 100%;
      }
    }
  }
}

@media (max-width: 360px) {
  .responsive-heading {
    font-size: clamp(14px, 4vw, 18px);
  }

  .responsive-description {
    font-size: 0.75rem;
  }

  .prejoin-parent-container {
    .ant-row {
      .prejoin-left-inner-container {
        .prejoin-mobile-view-heading {
          font-size: clamp(14px, 4vw, 18px);
        }
      }
      .prejoin-right-inner-container {
        .container-parent-form {
          .form-input-container {
            p {
              font-size: clamp(12px, 3vw, 14px);
            }
          }
        }
      }
    }
  }
}

// Update media queries to include button styles
@media (min-width: 1200px) {
  .prejoin-page-input,
  .ant-input-password,
  .prejoin-page-join-button {
    height: 45px;
    font-size: 14px;
    padding: 10px 15px;
  }
}

@media (min-width: 1600px) {
  .prejoin-page-input,
  .ant-input-password,
  .prejoin-page-join-button {
    height: 40px;
    font-size: 13px;
    padding: 8px 12px;
  }
}

// Extra small screens
@media (max-width: 320px) {
  .responsive-heading {
    font-size: clamp(12px, 3.5vw, 16px);
  }

  .responsive-description {
    font-size: 0.7rem;
  }

  .prejoin-parent-container {
    .ant-row {
      .prejoin-left-inner-container {
        .prejoin-mobile-view-heading {
          font-size: clamp(12px, 3.5vw, 16px);
        }
      }
      .prejoin-right-inner-container {
        .container-parent-form {
          .form-input-container {
            p {
              font-size: clamp(10px, 3vw, 12px);
            }
          }
        }
      }
    }
  }
}